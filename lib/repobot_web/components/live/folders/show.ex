defmodule RepobotWeb.Live.Folders.Show do
  use RepobotWeb, :live_view
  require Logger

  alias <PERSON>obot.{Folders, SourceFiles, Files}
  alias Repobot.Repo

  import RepobotWeb.CoreComponents

  def mount(%{"id" => id}, _session, socket) do
    folder = Folders.get_folder!(id)
    source_files = get_source_files(socket.assigns)

    # Preload files for all repositories (regular and template)
    all_repositories =
      (folder.repositories ++ folder.template_repositories)
      |> Enum.uniq_by(& &1.id)
      |> Enum.map(&Repo.preload(&1, :files))

    # Update folder struct with combined, preloaded repositories
    folder = %{folder | repositories: all_repositories}

    # Initialize loading states and get repositories that need refresh
    {loading_states, needs_refresh} = Repobot.Files.RepoTree.init_loading(all_repositories)

    # Start loading trees if connected and there are repos to refresh
    if connected?(socket) do
      # Subscribe to repository file changes
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_files")

      if Enum.empty?(needs_refresh) do
        # If no repos need refresh, trigger common files calculation directly
        send(self(), :calculate_common_files)
      else
        # Otherwise start loading trees for repos that need it
        Repobot.Files.RepoTree.load_trees(needs_refresh, self(), socket.assigns.current_user)
      end
    end

    # Fetch source files created *from* each template repository
    source_files_from_templates =
      folder.template_repositories
      |> Enum.reduce(%{}, fn repo, acc ->
        files = SourceFiles.list_source_files_created_from_repository(repo)
        Map.put(acc, repo.id, files)
      end)

    socket =
      socket
      |> assign(:folder, folder)
      |> assign(:source_files, source_files)
      |> assign(:source_files_from_templates, source_files_from_templates)
      |> assign(:common_files, [])
      |> assign(:loading_common_files, true)
      |> assign(:common_files_error, nil)
      |> assign(:loading_states, loading_states)
      # Track content refresh progress
      |> assign(:content_refresh_progress, nil)
      # Track similarity calculation progress
      |> assign(:similarity_progress, nil)
      # Keep this for the import dropdown
      |> assign(:importing_common_file, nil)
      |> assign(:show_edit_modal, false)
      |> assign(:editing_source_file, nil)
      |> assign(:show_import_dropdown, nil)
      |> assign(:repo_file_contents, %{})
      |> assign(:generating_template_for, nil)
      |> assign(:previewing_file, nil)
      |> assign(:page_title, folder.name)
      |> assign(:force_refresh_content, false)
      |> assign(:show_diff_modal, false)
      |> assign(:diff_repository, nil)
      |> assign(:diff_source_file, nil)
      |> assign(:diff_content, nil)
      |> assign(:available_repositories, [])
      # Add missing assign
      |> assign(:common_files_repo_states, %{})

    {:ok, socket}
  end

  def handle_event("add_source_file", %{"source_file_id" => source_file_id}, socket) do
    source_file = SourceFiles.get_source_file!(source_file_id)

    results =
      socket.assigns.folder.repositories
      |> non_template_repositories()
      |> Enum.map(fn repository ->
        Repobot.Repositories.add_source_file(repository, source_file)
      end)

    success_count =
      Enum.count(results, fn
        {:ok, _} -> true
        _ -> false
      end)

    error_count =
      Enum.count(results, fn
        {:error, _} -> true
        _ -> false
      end)

    message =
      case {success_count, error_count} do
        {s, 0} -> "Successfully added source file to #{s} repositories"
        {0, e} -> "Failed to add source file to #{e} repositories"
        {s, e} -> "Added source file to #{s} repositories, failed for #{e} repositories"
      end

    # Refresh the folder data to get updated repository associations
    folder = Folders.get_folder!(socket.assigns.folder.id)

    {:noreply,
     socket
     |> assign(:folder, folder)
     |> put_flash(if(error_count > 0, do: :error, else: :info), message)}
  end

  def handle_event("remove_source_file", %{"source_file_id" => source_file_id}, socket) do
    source_file = SourceFiles.get_source_file!(source_file_id)

    results =
      Enum.map(socket.assigns.folder.repositories, fn repository ->
        Repobot.Repositories.remove_source_file(repository, source_file)
      end)

    success_count =
      Enum.count(results, fn
        {:ok, _} -> true
        _ -> false
      end)

    error_count =
      Enum.count(results, fn
        {:error, _} -> true
        _ -> false
      end)

    message =
      case {success_count, error_count} do
        {s, 0} -> "Successfully removed source file from #{s} repositories"
        {0, e} -> "Failed to remove source file from #{e} repositories"
        {s, e} -> "Removed source file from #{s} repositories, failed for #{e} repositories"
      end

    # Refresh the folder data to get updated repository associations
    folder = Folders.get_folder!(socket.assigns.folder.id)

    {:noreply,
     socket
     |> assign(:folder, folder)
     |> put_flash(if(error_count > 0, do: :error, else: :info), message)}
  end

  def handle_event("show_import_dropdown", %{"path" => path}, socket) do
    # Get file content from template repositories only
    repo_contents =
      socket.assigns.folder.repositories
      |> Enum.filter(& &1.template)
      |> Enum.reduce(%{}, fn repo, acc ->
        repo = Repo.preload(repo, :files)

        # Find the file in the repository's files
        file_data = Enum.find(repo.files, &(&1.path == path))

        if file_data do
          # Use the stored content if available
          content = if file_data.content, do: file_data.content, else: ""

          Map.put(acc, repo.full_name, %{
            "content" => content,
            "size" => file_data.size
          })
        else
          acc
        end
      end)

    {:noreply,
     socket
     |> assign(:show_import_dropdown, path)
     |> assign(:repo_file_contents, repo_contents)}
  end

  def handle_event("hide_import_dropdown", _, socket) do
    {:noreply,
     socket
     |> assign(:show_import_dropdown, nil)
     |> assign(:repo_file_contents, %{})}
  end

  def handle_event("import_common_file", %{"path" => path, "repository" => repository}, socket) do
    Logger.info("Importing common file: #{path} from #{repository}")
    socket = assign(socket, :importing_common_file, path)

    repo = Enum.find(socket.assigns.folder.repositories, &(&1.full_name == repository))
    repo = Repobot.Repo.preload(repo, [:files, :folder, :template_folders])
    file_data = Enum.find(repo.files, &(&1.path == path))

    if file_data && file_data.content do
      # Create source file with content and associate with repositories
      attrs = %{
        name: file_data.path,
        content: file_data.content,
        target_path: file_data.path,
        organization_id: socket.assigns.current_organization.id,
        source_repository_id: repo.id,
        folder_id: socket.assigns.folder.id,
        user_id: socket.assigns.current_user.id
      }

      # If importing from a template repository, associate with all repositories
      # from all folders the template is in
      repositories_to_associate =
        if repo.template do
          # Get repositories from both the primary folder and template folders
          folders = [repo.folder | repo.template_folders] |> Enum.reject(&is_nil/1)

          folders
          |> Enum.flat_map(fn folder ->
            Repobot.Repo.preload(folder, :repositories).repositories
          end)
          |> Enum.uniq_by(& &1.id)
        else
          # Keep existing behavior for non-template repositories
          socket.assigns.folder.repositories
        end

      case SourceFiles.import_file(
             attrs,
             repositories_to_associate
           ) do
        {:ok, _source_file} ->
          folder = Folders.get_folder!(socket.assigns.folder.id)
          source_files = get_source_files(socket.assigns)

          {:noreply,
           socket
           |> assign(:folder, folder)
           |> assign(:source_files, source_files)
           |> assign(:importing_common_file, nil)
           |> assign(:show_import_dropdown, nil)
           |> assign(:repo_file_contents, %{})
           |> put_flash(:info, "Successfully imported #{path} and added to repositories")}

        {:error, reason} ->
          Logger.error("Failed to import file: #{inspect(reason)}")

          {:noreply,
           socket
           |> assign(:importing_common_file, nil)
           |> assign(:show_import_dropdown, nil)
           |> assign(:repo_file_contents, %{})
           |> put_flash(:error, "Failed to import file: #{reason}")}
      end
    else
      {:noreply,
       socket
       |> assign(:importing_common_file, nil)
       |> assign(:show_import_dropdown, nil)
       |> assign(:repo_file_contents, %{})
       |> put_flash(:error, "File content not available in database")}
    end
  end

  def handle_event("hide_edit_modal", _, socket) do
    {:noreply,
     socket
     |> assign(:show_edit_modal, false)
     |> assign(:editing_source_file, nil)}
  end

  def handle_event("show_edit_modal", %{"source_file_id" => id}, socket) do
    source_file = SourceFiles.get_source_file!(id)

    {:noreply,
     socket
     |> assign(:show_edit_modal, true)
     |> assign(:editing_source_file, source_file)}
  end

  def handle_event("save_source_file", %{"content" => content}, socket) do
    case SourceFiles.update_source_file(socket.assigns.editing_source_file, %{content: content}) do
      {:ok, _source_file} ->
        source_files = get_source_files(socket.assigns)

        {:noreply,
         socket
         |> assign(:source_files, source_files)
         |> assign(:show_edit_modal, false)
         |> assign(:editing_source_file, nil)
         |> put_flash(:info, "Source file updated successfully")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update source file")}
    end
  end

  def handle_event("move_to_global", %{"source_file_id" => id}, socket) do
    source_file = SourceFiles.get_source_file!(id)

    # Remove the source file from the current folder
    case SourceFiles.remove_source_file_from_folder(source_file, socket.assigns.folder) do
      {1, nil} ->
        source_files = get_source_files(socket.assigns)

        {:noreply,
         socket
         |> assign(:source_files, source_files)
         |> put_flash(:info, "Source file moved to global")}

      {0, nil} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to move source file to global")}
    end
  end

  def handle_event("move_to_folder", %{"source_file_id" => id}, socket) do
    source_file = SourceFiles.get_source_file!(id)

    # Add the source file to the current folder
    case SourceFiles.add_source_file_to_folder(source_file, socket.assigns.folder) do
      {:ok, _} ->
        source_files = get_source_files(socket.assigns)

        {:noreply,
         socket
         |> assign(:source_files, source_files)
         |> put_flash(:info, "Source file moved to folder")}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to move source file to folder")}
    end
  end

  def handle_event("show_file_preview", %{"path" => path, "repository" => repository}, socket) do
    {:noreply,
     socket
     |> assign(:previewing_file, %{
       path: path,
       repository: repository,
       content: get_in(socket.assigns.repo_file_contents, [repository, "content"])
     })}
  end

  def handle_event("hide_file_preview", _, socket) do
    {:noreply, assign(socket, :previewing_file, nil)}
  end

  def handle_event("generate_template", %{"path" => path}, socket) do
    # Find the file with the given path in the common files
    case Enum.find(socket.assigns.common_files, &(&1["path"] == path)) do
      %{"similarity" => similarity} = _file when similarity >= 75 ->
        socket = assign(socket, :generating_template_for, path)

        # Get the two repositories with the highest similarity score
        repo_contents =
          socket.assigns.folder.repositories
          |> Enum.reduce(%{}, fn repo, acc ->
            repo = Repo.preload(repo, :files)

            # Find the file in the repository's files
            file_data = Enum.find(repo.files, &(&1.path == path))

            if file_data do
              Map.put(acc, repo.full_name, %{
                "content" => file_data.content,
                "path" => file_data.path
              })
            else
              acc
            end
          end)

        # Sort repositories by content similarity and take the top 2
        case find_most_similar_pair(repo_contents) do
          {repo1, data1, repo2, data2} ->
            case Repobot.AI.backend().generate_template(
                   data1["content"],
                   data2["content"],
                   data1["path"],
                   Map.new(Repobot.TemplateContext.available_variables()),
                   socket.assigns.current_organization
                 ) do
              {:ok, template} ->
                # Create a source file with the template
                case SourceFiles.create_source_file(%{
                       name: path,
                       content: template,
                       is_template: true,
                       user_id: socket.assigns.current_user.id,
                       target_path: path,
                       organization_id: socket.assigns.current_user.default_organization_id
                     }) do
                  {:ok, source_file} ->
                    # Associate with repositories and determine tags
                    with {:ok, source_file_with_repos} <-
                           associate_with_repositories(
                             source_file,
                             socket.assigns.folder.repositories
                           ),
                         {:ok, _} <-
                           SourceFiles.add_source_file_to_folder(
                             source_file,
                             socket.assigns.folder
                           ),
                         {:ok, _tagged_source_file} <-
                           SourceFiles.determine_tags(source_file_with_repos) do
                      source_files = get_source_files(socket.assigns)

                      {:noreply,
                       socket
                       |> assign(:source_files, source_files)
                       |> assign(:generating_template_for, nil)
                       |> put_flash(
                         :info,
                         "Successfully generated template from #{repo1} and #{repo2}"
                       )}
                    else
                      {:error, _} ->
                        {:noreply,
                         socket
                         |> assign(:generating_template_for, nil)
                         |> put_flash(:error, "Failed to associate template with repositories")}
                    end

                  {:error, _changeset} ->
                    {:noreply,
                     socket
                     |> assign(:generating_template_for, nil)
                     |> put_flash(:error, "Failed to create template")}
                end

              {:error, reason} ->
                {:noreply,
                 socket
                 |> assign(:generating_template_for, nil)
                 |> put_flash(:error, "Failed to generate template: #{reason}")}
            end

          nil ->
            {:noreply,
             socket
             |> assign(:generating_template_for, nil)
             |> put_flash(:error, "Could not find two similar files to generate template from")}
        end

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "File similarity is too low for template generation")}
    end
  end

  def handle_event("refresh_common_files", _, socket) do
    socket = assign(socket, :force_refresh_content, true)
    send(self(), :calculate_common_files)
    {:noreply, socket}
  end

  def handle_event(
        "show_diff",
        %{"source_file_id" => source_file_id, "repository" => repository},
        socket
      ) do
    [owner, repo] = String.split(repository, "/")
    source_file = SourceFiles.get_source_file!(source_file_id)

    # Get repository and its files from database
    repository =
      Repobot.Repository
      |> Repo.get_by!(owner: owner, name: repo)
      |> Repo.preload([:folder, :files])

    # Get the file content from our database
    repo_content =
      repository.files
      |> Enum.find(&(&1.path == source_file.target_path))
      |> case do
        %{content: content} when not is_nil(content) -> content
        _ -> nil
      end

    # Get folder settings if repository belongs to a folder
    folder_settings = if repository.folder_id, do: repository.folder.settings, else: %{}

    # Merge settings: folder_settings as base, repo_settings override them
    settings =
      folder_settings
      |> Map.merge(repository.settings)
      |> Map.new(fn {k, v} -> {to_string(k), format_setting_value(v)} end)

    # Merge repository data with settings for template variables
    template_vars =
      Map.merge(
        Repobot.TemplateContext.format_repository_data(repository.data),
        %{"settings" => settings}
      )

    # Render the source file template for this repository if it's a template
    rendered_content =
      if source_file.is_template do
        case SourceFiles.render_template(source_file, template_vars) do
          {:ok, content} -> content
          {:error, _reason} -> source_file.content
        end
      else
        source_file.content
      end

    # For template repository section, make all repositories available for comparison
    available_repositories =
      if repository.template do
        socket.assigns.folder.repositories
        |> Enum.reject(& &1.template)
        |> Enum.map(& &1.full_name)
      else
        []
      end

    {:noreply,
     socket
     |> assign(:show_diff_modal, true)
     |> assign(:diff_repository, repository)
     |> assign(:diff_source_file, source_file)
     |> assign(:available_repositories, available_repositories)
     |> push_event("render_diff", %{source: rendered_content, target: repo_content || ""})}
  end

  def handle_event("change_diff_repository", %{"repository" => repository}, socket) do
    [owner, repo] = String.split(repository, "/")
    source_file = socket.assigns.diff_source_file

    # Get repository and its files from database
    repository =
      Repobot.Repository
      |> Repo.get_by!(owner: owner, name: repo)
      |> Repo.preload([:folder, :files])

    # Get the file content from our database
    repo_content =
      repository.files
      |> Enum.find(&(&1.path == source_file.target_path))
      |> case do
        %{content: content} when not is_nil(content) -> content
        _ -> nil
      end

    # Get folder settings if repository belongs to a folder
    folder_settings = if repository.folder_id, do: repository.folder.settings, else: %{}

    # Merge settings: folder_settings as base, repo_settings override them
    settings =
      folder_settings
      |> Map.merge(repository.settings)
      |> Map.new(fn {k, v} -> {to_string(k), format_setting_value(v)} end)

    # Merge repository data with settings for template variables
    template_vars =
      Map.merge(
        Repobot.TemplateContext.format_repository_data(repository.data),
        %{"settings" => settings}
      )

    # Render the source file template for this repository if it's a template
    rendered_content =
      if source_file.is_template do
        case SourceFiles.render_template(source_file, template_vars) do
          {:ok, content} -> content
          {:error, _reason} -> source_file.content
        end
      else
        source_file.content
      end

    # Keep the available repositories list since we're in template context
    available_repositories =
      socket.assigns.folder.repositories
      |> Enum.reject(& &1.template)
      |> Enum.map(& &1.full_name)

    {:noreply,
     socket
     |> assign(:show_diff_modal, true)
     |> assign(:diff_repository, repository)
     |> assign(:diff_source_file, source_file)
     |> assign(:available_repositories, available_repositories)
     |> push_event("render_diff", %{source: rendered_content, target: repo_content || ""})}
  end

  def handle_event("hide_diff_modal", _, socket) do
    {:noreply,
     socket
     |> assign(:show_diff_modal, false)
     |> assign(:diff_repository, nil)
     |> assign(:diff_source_file, nil)
     |> assign(:diff_content, nil)
     |> assign(:available_repositories, [])}
  end

  def handle_event("delete_folder", _, socket) do
    case Folders.delete_folder(socket.assigns.folder) do
      {:ok, _folder} ->
        {:noreply,
         socket
         |> put_flash(:info, "Folder deleted successfully")
         |> push_navigate(to: ~p"/repositories")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to delete folder")}
    end
  end

  def handle_event("toggle_star", _, socket) do
    case Folders.toggle_starred(socket.assigns.folder) do
      {:ok, folder} ->
        {:noreply, assign(socket, :folder, folder)}

      {:error, _changeset} ->
        {:noreply, socket}
    end
  end

  def handle_info(:refresh_common_files, socket) do
    socket = assign(socket, :force_refresh_content, true)
    send(self(), :calculate_common_files)
    {:noreply, socket}
  end

  def handle_info({:tree_loaded, repo_id}, socket) do
    {loading_states, all_loaded?} =
      Repobot.Files.RepoTree.handle_tree_message(
        socket.assigns.loading_states,
        {:tree_loaded, repo_id}
      )

    socket = assign(socket, :loading_states, loading_states)

    if all_loaded? do
      # Reload folder with fresh repository data
      folder = Folders.get_folder!(socket.assigns.folder.id)

      # Combine regular and template repositories, preload files
      all_repositories =
        (folder.repositories ++ folder.template_repositories)
        |> Enum.uniq_by(& &1.id)
        |> Enum.map(&Repo.preload(&1, :files))

      # Update folder struct with combined, preloaded repositories
      folder = %{folder | repositories: all_repositories}
      socket = assign(socket, :folder, folder)

      # Find common files first
      case Files.find_common_files(all_repositories) do
        {:ok, common_files} ->
          # If common files are found, assign them and start content refresh
          socket =
            socket
            |> assign(:common_files, common_files)
            # Indicate loading is ongoing
            |> assign(:loading_common_files, true)
            # Reset similarity progress
            |> assign(:similarity_progress, nil)
            # Start content progress
            |> assign(:content_refresh_progress, 0)

          if Enum.empty?(common_files) do
            # If no common files, we are done loading
            {:noreply, assign(socket, :loading_common_files, false)}
          else
            Files.RepoTree.refresh_file_content(
              all_repositories,
              self(),
              socket.assigns.current_user
            )

            {:noreply, socket}
          end

        {:error, reason} ->
          # Handle error finding common files
          {:noreply,
           socket
           |> assign(:loading_common_files, false)
           |> assign(:common_files_error, reason)}
      end
    else
      # Not all trees loaded yet, just update state
      {:noreply, socket}
    end
  end

  def handle_info({:tree_load_failed, repo_id, reason}, socket) do
    {loading_states, _} =
      Repobot.Files.RepoTree.handle_tree_message(
        socket.assigns.loading_states,
        {:tree_load_failed, repo_id, reason}
      )

    {:noreply,
     socket
     |> assign(:loading_states, loading_states)
     |> put_flash(:error, "Failed to load repository files: #{inspect(reason)}")}
  end

  def handle_info({:all_trees_loaded}, socket) do
    send(self(), :calculate_common_files)
    {:noreply, socket}
  end

  def handle_info({:common_files_loaded, files}, socket) do
    {:noreply,
     socket
     |> assign(:common_files, files)
     |> assign(:loading_common_files, false)
     |> assign(:common_files_error, nil)
     # Reset force refresh flag
     |> assign(:force_refresh, nil)}
  end

  def handle_info({:common_files_load_failed, reason}, socket) do
    Logger.error("Failed to load common files: #{inspect(reason)}")

    {:noreply,
     socket
     |> assign(:loading_common_files, false)
     |> assign(:common_files_error, reason)}
  end

  def handle_info({:repo_file_state_update, repo_id, state, message}, socket) do
    {:noreply,
     assign(
       socket,
       :common_files_repo_states,
       Map.put(socket.assigns.common_files_repo_states, repo_id, {state, message})
     )}
  end

  def handle_info(:calculate_common_files, socket) do
    lv_pid = self()
    folder = Folders.get_folder!(socket.assigns.folder.id)

    # Combine regular and template repositories, preload files
    all_repositories =
      (folder.repositories ++ folder.template_repositories)
      |> Enum.uniq_by(& &1.id)
      |> Enum.map(&Repo.preload(&1, :files))

    # Update folder struct with combined, preloaded repositories
    folder = %{folder | repositories: all_repositories}

    # Find common files
    case Files.find_common_files(all_repositories) do
      {:ok, common_files} ->
        # If force refresh is set (manual button click), refresh content first.
        # Otherwise (initial load where files existed), calculate similarity directly.
        socket =
          socket
          |> assign(:folder, folder)
          |> assign(:common_files, common_files)
          # Start loading indicator
          |> assign(:loading_common_files, true)
          # Reset progress bars
          |> assign(:similarity_progress, nil)
          |> assign(:content_refresh_progress, nil)

        if Enum.empty?(common_files) do
          # If no common files, we are done loading
          {:noreply, assign(socket, :loading_common_files, false)}
        else
          if socket.assigns.force_refresh_content do
            # Manual refresh: Start content refresh process
            socket = assign(socket, :content_refresh_progress, 0)

            Files.RepoTree.refresh_file_content(
              all_repositories,
              lv_pid,
              socket.assigns.current_user
            )

            {:noreply, socket}
          else
            # Initial load (files already present): Calculate similarity directly
            socket = assign(socket, :similarity_progress, 0)
            Files.calculate_common_files_similarity(common_files, all_repositories, lv_pid)
            {:noreply, socket}
          end
        end

      {:error, reason} ->
        {:noreply,
         socket
         |> assign(:loading_common_files, false)
         |> assign(:common_files_error, reason)}
    end
  end

  def handle_info({:content_refresh_progress, progress}, socket) do
    {:noreply, assign(socket, :content_refresh_progress, progress)}
  end

  def handle_info({:content_refresh_complete}, socket) do
    # Content refresh is complete (either from initial load or manual refresh).
    # Now, start similarity calculation.

    # Reload folder and repositories to get the updated file content
    folder = Folders.get_folder!(socket.assigns.folder.id)

    all_repositories =
      (folder.repositories ++ folder.template_repositories)
      |> Enum.uniq_by(& &1.id)
      |> Enum.map(&Repo.preload(&1, :files))

    folder = %{folder | repositories: all_repositories}

    socket =
      socket
      # Assign reloaded folder with content
      |> assign(:folder, folder)
      |> assign(:content_refresh_progress, 100)
      # Start similarity progress
      |> assign(:similarity_progress, 0)

    # Use the reloaded repositories list
    Files.calculate_common_files_similarity(
      socket.assigns.common_files,
      all_repositories,
      self()
    )

    {:noreply, socket}
  end

  def handle_info({:content_refresh_error, reason}, socket) do
    {:noreply,
     socket
     |> assign(:loading_common_files, false)
     |> assign(:common_files_error, reason)}
  end

  def handle_info({:similarity_progress, progress}, socket) do
    {:noreply, assign(socket, :similarity_progress, progress)}
  end

  def handle_info({:similarity_complete, files}, socket) do
    {:noreply,
     socket
     |> assign(:common_files, files)
     |> assign(:loading_common_files, false)
     |> assign(:similarity_progress, 100)
     # Reset content refresh progress as well
     |> assign(:content_refresh_progress, nil)
     # Reset force refresh flag
     |> assign(:force_refresh_content, nil)}
  end

  def handle_info({:similarity_error, reason}, socket) do
    {:noreply,
     socket
     |> assign(:loading_common_files, false)
     |> assign(:common_files_error, reason)}
  end

  # Handle repository files updated events from push webhooks
  def handle_info({:repository_files_updated, repository_id, _metadata}, socket) do
    # Check if any of the repositories in this folder were updated
    folder_repo_ids =
      (socket.assigns.folder.repositories ++ socket.assigns.folder.template_repositories)
      |> Enum.map(& &1.id)
      |> MapSet.new()

    if MapSet.member?(folder_repo_ids, repository_id) do
      # One of our repositories was updated, refresh common files
      send(self(), :refresh_common_files)
      {:noreply, put_flash(socket, :info, "Repository files updated automatically")}
    else
      {:noreply, socket}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="mb-8">
        <nav class="mb-6">
          <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
            <li>
              <.link navigate={~p"/repositories"} class="hover:text-indigo-600">
                Repositories
              </.link>
            </li>
            <li>•</li>
            <li class="font-medium text-slate-900">{@folder.name}</li>
          </ol>
        </nav>
        <div class="flex justify-between items-center mb-8">
          <div class="flex items-center gap-2">
            <h1 class="text-2xl font-semibold text-slate-900">{@folder.name}</h1>
            <button
              phx-click="toggle_star"
              class={"text-slate-400 hover:text-slate-500 #{if @folder.starred, do: "text-yellow-400 hover:text-yellow-500"}"}
              aria-label={if @folder.starred, do: "Unstar folder", else: "Star folder"}
            >
              <.icon name="hero-star" class="w-6 h-6" />
            </button>
          </div>
          <div class="flex items-center gap-2">
            <.link
              navigate={~p"/folders/#{@folder}/edit"}
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-800 bg-indigo-50 hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-400 transition-colors duration-150"
            >
              <.icon name="hero-pencil" class="w-4 h-4 mr-2 text-indigo-600" /> Edit
            </.link>
            <button
              phx-click="delete_folder"
              data-confirm="Are you sure you want to delete this folder? This will unassign all repositories from this folder."
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-rose-800 bg-rose-50 hover:bg-rose-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-rose-400 transition-colors duration-150"
            >
              <.icon name="hero-trash" class="w-4 h-4 mr-2 text-rose-600" /> Delete
            </button>
          </div>
        </div>
      </div>

      <%= if not Enum.empty?(@folder.template_repositories) do %>
        <div class="mb-8">
          <div class="px-6 py-5 bg-emerald-50 rounded-lg shadow-sm border-2 border-emerald-200 mb-4">
            <div class="flex items-center gap-2">
              <.icon name="hero-arrow-path-rounded-square" class="w-5 h-5 text-emerald-600" />
              <h2 class="text-lg font-medium text-emerald-900">Template Repositories</h2>
            </div>
            <p class="mt-1 text-sm text-emerald-700">
              These repositories serve as templates for other repositories in this folder.
            </p>
          </div>
          <div class={["grid gap-4", template_grid_columns(@folder.template_repositories)]}>
            <%= for template_repo <- @folder.template_repositories do %>
              <div class="bg-white rounded-lg shadow-sm border border-emerald-200 hover:shadow-md transition-shadow duration-150">
                <div class="px-4 py-3 border-b border-emerald-100 bg-emerald-50/50 rounded-t-lg">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <h3 class="text-sm font-medium text-slate-900">
                        <.link
                          navigate={~p"/repositories/#{template_repo}"}
                          class="text-emerald-600 hover:text-emerald-800"
                        >
                          {template_repo.full_name}
                        </.link>
                      </h3>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-100 text-emerald-800">
                        Template
                      </span>
                    </div>
                    <%= case Repobot.Files.RepoTree.get_loading_state(@loading_states, template_repo.id) do %>
                      <% :loading -> %>
                        <div class="flex items-center text-xs text-emerald-600">
                          <.icon name="hero-arrow-path" class="w-3 h-3 animate-spin mr-1" />
                          Loading...
                        </div>
                      <% _ -> %>
                        <div class="flex items-center text-xs text-emerald-600">
                          <.icon name="hero-chevron-right" class="w-3 h-3 mr-1" />
                        </div>
                    <% end %>
                  </div>
                </div>
                <div class="px-4 py-3">
                  <h4 class="text-xs font-medium text-emerald-800 mb-2">Source Files:</h4>
                  <div class="flex flex-wrap gap-1.5">
                    <%= for source_file <- @source_files_from_templates[template_repo.id] || [] do %>
                      <button
                        phx-click="show_diff"
                        phx-value-source_file_id={source_file.id}
                        phx-value-repository={template_repo.full_name}
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-emerald-700 bg-white border border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 transition-colors duration-150"
                      >
                        {source_file.name}
                      </button>
                    <% end %>
                    <%= if Enum.empty?(@source_files_from_templates[template_repo.id] || []) do %>
                      <span class="text-xs text-emerald-600/75 italic">No source files yet</span>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>

      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm border border-slate-200">
            <div class="px-6 py-5 border-b border-slate-200 bg-slate-50 rounded-t-lg">
              <h2 class="text-lg font-medium text-slate-900">Repositories</h2>
            </div>
            <div id="repository-list" class="divide-y divide-slate-200" phx-hook="RepositoryList">
              <%= for repo <- non_template_repositories(@folder.repositories) do %>
                <div class="px-6 py-4" data-repository aria-expanded="false">
                  <div class="flex items-center justify-between cursor-pointer">
                    <div class="flex items-center gap-2">
                      <div data-chevron class="transform transition-transform duration-200">
                        <%= case Repobot.Files.RepoTree.get_loading_state(@loading_states, repo.id) do %>
                          <% :loading -> %>
                            <.icon
                              name="hero-arrow-path"
                              class="w-4 h-4 animate-spin text-indigo-600"
                            />
                          <% _ -> %>
                            <.icon name="hero-chevron-right" class="w-4 h-4 text-slate-400" />
                        <% end %>
                      </div>
                      <h3 class="text-sm font-medium text-slate-900">
                        {repo.full_name}
                      </h3>
                    </div>
                  </div>
                  <div data-source-files class="mt-3 pl-6 hidden">
                    <%= if Enum.empty?(repo.source_files || []) do %>
                      <p class="text-sm text-slate-500 italic">No source files</p>
                    <% else %>
                      <div class="flex flex-wrap gap-1.5">
                        <%= for source_file <- (repo.source_files || []) do %>
                          <button
                            phx-click="show_diff"
                            phx-value-source_file_id={source_file.id}
                            phx-value-repository={repo.full_name}
                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-slate-600 bg-slate-100 hover:bg-slate-200 hover:text-slate-900 transition-colors duration-150"
                          >
                            {source_file.name}
                          </button>
                        <% end %>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <div class="lg:col-span-3">
          <.expandable_container
            id="folder-source-files"
            title="Folder Source Files"
            description="Source files that belong to this folder"
            expanded={true}
            class="mb-6"
          >
            <%= for source_file <- folder_source_files(@source_files, @folder) do %>
              <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="text-sm font-medium text-slate-900">
                      <.link
                        navigate={~p"/source-files/#{source_file.id}"}
                        class="inline-flex items-center text-indigo-600 hover:text-indigo-800"
                      >
                        {source_file.name}
                      </.link>
                    </h3>
                    <p class="mt-1 text-xs text-slate-500">
                      {repositories_with_source_file(@folder.repositories, source_file)} of {length(
                        @folder.repositories
                      )} repositories
                    </p>
                    <%= for pr <- Enum.filter(source_file.pull_requests, &(&1.status == "open")) do %>
                      <div class="mt-2">
                        <.pull_request_badge pull_request={pr} />
                      </div>
                    <% end %>
                    <%= if Map.get(source_file, :pr_results) do %>
                      <%= for {repo_name, result} <- source_file.pr_results do %>
                        <div class="mt-2">
                          <%= case result do %>
                            <% {:ok, "No changes needed - content is identical"} -> %>
                              <div class="flex items-center gap-2">
                                <.icon name="hero-check-circle" class="w-4 h-4 text-emerald-500" />
                                <span class="text-xs text-emerald-600">
                                  {repo_name} is up to date
                                </span>
                              </div>
                            <% {:ok, url} -> %>
                              <.pull_request_badge pull_request={
                                %{
                                  pull_request_url: url,
                                  pull_request_number: extract_pr_number(url)
                                }
                              } />
                            <% {:error, reason} -> %>
                              <div class="flex items-center gap-2">
                                <.icon name="hero-exclamation-triangle" class="w-4 h-4 text-red-500" />
                                <span class="text-xs text-red-600">
                                  Error for {repo_name}: {reason}
                                </span>
                              </div>
                          <% end %>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                  <div class="flex items-center gap-2">
                    <button
                      phx-click="show_edit_modal"
                      phx-value-source_file_id={source_file.id}
                      class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
                    >
                      Edit
                    </button>
                    <button
                      phx-click="move_to_global"
                      phx-value-source_file_id={source_file.id}
                      class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
                    >
                      Move to Global
                    </button>
                    <%= if all_repositories_have_source_file?(@folder.repositories, source_file) do %>
                      <button
                        phx-click="remove_source_file"
                        phx-value-source_file_id={source_file.id}
                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150"
                      >
                        Remove from All
                      </button>
                    <% else %>
                      <button
                        phx-click="add_source_file"
                        phx-value-source_file_id={source_file.id}
                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
                      >
                        Add to All
                      </button>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
            <%= if Enum.empty?(folder_source_files(@source_files, @folder)) do %>
              <div class="px-6 py-4 text-sm text-slate-500 italic">
                No source files belong to this folder yet.
              </div>
            <% end %>
          </.expandable_container>

          <.expandable_container
            id="global-source-files"
            title="Global Source Files"
            description="Source files available to all folders"
            expanded={false}
            class="mb-6"
          >
            <%= for source_file <- global_source_files(@source_files) do %>
              <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="text-sm font-medium text-slate-900">
                      <.link
                        navigate={~p"/source-files/#{source_file}"}
                        class="inline-flex items-center text-indigo-600 hover:text-indigo-800"
                      >
                        {source_file.name}
                      </.link>
                    </h3>
                    <p class="mt-1 text-xs text-slate-500">
                      {repositories_with_source_file(@folder.repositories, source_file)} of {length(
                        @folder.repositories
                      )} repositories
                    </p>
                    <%= for pr <- Enum.filter(source_file.pull_requests, &(&1.status == "open")) do %>
                      <div class="mt-2">
                        <.pull_request_badge pull_request={pr} />
                      </div>
                    <% end %>
                    <%= if Map.get(source_file, :pr_results) do %>
                      <%= for {repo_name, result} <- source_file.pr_results do %>
                        <div class="mt-2">
                          <%= case result do %>
                            <% {:ok, "No changes needed - content is identical"} -> %>
                              <div class="flex items-center gap-2">
                                <.icon name="hero-check-circle" class="w-4 h-4 text-emerald-500" />
                                <span class="text-xs text-emerald-600">
                                  {repo_name} is up to date
                                </span>
                              </div>
                            <% {:ok, url} -> %>
                              <.pull_request_badge pull_request={
                                %{
                                  pull_request_url: url,
                                  pull_request_number: extract_pr_number(url)
                                }
                              } />
                            <% {:error, reason} -> %>
                              <div class="flex items-center gap-2">
                                <.icon name="hero-exclamation-triangle" class="w-4 h-4 text-red-500" />
                                <span class="text-xs text-red-600">
                                  Error for {repo_name}: {reason}
                                </span>
                              </div>
                          <% end %>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                  <div class="flex items-center gap-2">
                    <button
                      phx-click="show_edit_modal"
                      phx-value-source_file_id={source_file.id}
                      class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
                    >
                      Edit
                    </button>
                    <button
                      phx-click="move_to_folder"
                      phx-value-source_file_id={source_file.id}
                      class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
                    >
                      Move to folder
                    </button>
                    <%= if all_repositories_have_source_file?(@folder.repositories, source_file) do %>
                      <button
                        phx-click="remove_source_file"
                        phx-value-source_file_id={source_file.id}
                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150"
                      >
                        Remove from All
                      </button>
                    <% else %>
                      <button
                        phx-click="add_source_file"
                        phx-value-source_file_id={source_file.id}
                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
                      >
                        Add to All
                      </button>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
            <%= if Enum.empty?(global_source_files(@source_files)) do %>
              <div class="px-6 py-4 text-sm text-slate-500 italic">
                No global source files available.
              </div>
            <% end %>
          </.expandable_container>

          <.expandable_container
            id="common-files"
            title="Common Files"
            description="Files that exist in all or some of the repositories in this folder"
            expanded={true}
          >
            <:right_content>
              <button
                phx-click="refresh_common_files"
                class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
                disabled={@loading_common_files}
              >
                <%= if @loading_common_files do %>
                  <.icon name="hero-arrow-path" class="w-4 h-4 animate-spin mr-2" /> Refreshing...
                <% else %>
                  <.icon name="hero-arrow-path" class="w-4 h-4 mr-2" /> Refresh
                <% end %>
              </button>
            </:right_content>
            <%= if @loading_common_files do %>
              <div class="px-6 py-8">
                <div class="flex flex-col items-center gap-4">
                  <%= if @content_refresh_progress != nil do %>
                    <div class="w-full max-w-lg">
                      <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-slate-600">Refreshing file content...</span>
                        <span class="text-sm text-slate-600">{@content_refresh_progress}%</span>
                      </div>
                      <div class="w-full bg-slate-200 rounded-full h-2">
                        <div
                          class="bg-indigo-600 h-2 rounded-full transition-all duration-200"
                          style={"width: #{@content_refresh_progress}%"}
                        >
                        </div>
                      </div>
                    </div>
                  <% end %>

                  <%= if @similarity_progress != nil do %>
                    <div class="w-full max-w-lg">
                      <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-slate-600">Calculating file similarities...</span>
                        <span class="text-sm text-slate-600">{@similarity_progress}%</span>
                      </div>
                      <div class="w-full bg-slate-200 rounded-full h-2">
                        <div
                          class="bg-indigo-600 h-2 rounded-full transition-all duration-200"
                          style={"width: #{@similarity_progress}%"}
                        >
                        </div>
                      </div>
                    </div>
                  <% end %>

                  <%= if @content_refresh_progress == nil and @similarity_progress == nil do %>
                    <div class="inline-flex items-center gap-2">
                      <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600">
                      </div>
                      <span class="text-sm text-slate-600">Finding common files...</span>
                    </div>
                  <% end %>
                </div>
              </div>
            <% else %>
              <%= if @common_files_error do %>
                <div class="px-6 py-8">
                  <div class="flex items-center justify-center gap-2 text-red-600">
                    <.icon name="hero-exclamation-circle" class="w-5 h-5" />
                    <span>Error loading common files: {@common_files_error}</span>
                  </div>
                </div>
              <% else %>
                <%= for file <- @common_files do %>
                  <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                      <div>
                        <h3 class="text-sm font-semibold text-slate-900">
                          {Path.basename(file["path"])}
                        </h3>
                        <p class="mt-0.5 text-xs text-slate-500 font-mono">
                          {file["path"]}
                        </p>
                        <%= if Map.has_key?(file, "similarity") do %>
                          <div class="mt-2">
                            <div class="flex items-center">
                              <div class="w-32 bg-slate-200 rounded-full h-2 mr-2">
                                <div
                                  class={similarity_color_class(file["similarity"])}
                                  style={"width: #{file["similarity"]}%"}
                                >
                                </div>
                              </div>
                              <span class="text-xs text-slate-600">
                                {file["similarity"]}% similar
                              </span>
                            </div>
                          </div>
                        <% end %>
                      </div>
                      <div class="flex items-center gap-2">
                        <%= if not is_imported?(file, @folder.repositories) and Map.get(file, "similarity", 0) >= 75 do %>
                          <button
                            phx-click="generate_template"
                            phx-value-path={file["path"]}
                            phx-disable-with="Generating Template..."
                            data-button="generate-template"
                            data-file-path={file["path"]}
                            class="inline-flex items-center gap-2 px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
                          >
                            <span>Generate Template</span>
                          </button>
                        <% end %>
                        <%= if is_imported?(file, @folder.repositories) do %>
                          <span class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-700">
                            Already Imported
                          </span>
                        <% else %>
                          <div class="relative">
                            <button
                              phx-click="show_import_dropdown"
                              phx-value-path={file["path"]}
                              disabled={@importing_common_file == file["path"]}
                              class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <%= if @importing_common_file == file["path"] do %>
                                Importing...
                              <% else %>
                                Import from...
                              <% end %>
                            </button>

                            <%= if @show_import_dropdown == file["path"] do %>
                              <div class="absolute right-0 mt-2 w-72 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                                <div class="py-1" role="menu">
                                  <div class="px-4 py-2 text-xs text-slate-500 border-b border-slate-200">
                                    Select repository to import from:
                                  </div>
                                  <%= for {repo_name, data} <- @repo_file_contents do %>
                                    <div class="flex items-center justify-between px-4 py-2 text-sm hover:bg-slate-100">
                                      <button
                                        phx-click="import_common_file"
                                        phx-value-path={file["path"]}
                                        phx-value-repository={repo_name}
                                        phx-disable-with="Importing..."
                                        class="flex-1 text-left text-slate-700"
                                        role="menuitem"
                                      >
                                        <div class="flex items-center justify-between">
                                          <span>{repo_name}</span>
                                          <span class="text-xs text-slate-500">
                                            {format_size(data["size"])}
                                          </span>
                                        </div>
                                      </button>
                                      <button
                                        phx-click="show_file_preview"
                                        phx-value-path={file["path"]}
                                        phx-value-repository={repo_name}
                                        class="ml-2 p-1 text-slate-400 hover:text-slate-600"
                                        title="Preview file"
                                      >
                                        <.icon name="hero-eye" class="w-4 h-4" />
                                      </button>
                                    </div>
                                  <% end %>
                                </div>
                              </div>

                              <div phx-click="hide_import_dropdown" class="fixed inset-0 z-0"></div>
                            <% end %>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  </div>
                <% end %>
                <%= if Enum.empty?(@common_files) do %>
                  <div class="px-6 py-4 text-sm text-slate-500 italic">
                    No common files found across all repositories.
                  </div>
                <% end %>
              <% end %>
            <% end %>
          </.expandable_container>
        </div>
      </div>
    </div>

    <%= if @show_edit_modal and @editing_source_file do %>
      <div class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4">
          <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
            <h3 class="text-lg font-medium text-slate-900">
              Edit {@editing_source_file.name}
            </h3>
            <button phx-click="hide_edit_modal" class="text-slate-400 hover:text-slate-500">
              <.icon name="hero-x-mark" class="w-5 h-5" />
            </button>
          </div>
          <div class="p-6">
            <.form for={%{}} id="edit-source-file-form" phx-submit="save_source_file">
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-slate-700 mb-1">Content</label>
                  <textarea
                    name="content"
                    class="block w-full rounded-md border-slate-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm font-mono"
                    rows="20"
                  ><%= @editing_source_file.content %></textarea>
                </div>
              </div>
              <div class="mt-6 flex justify-end gap-3">
                <button
                  type="button"
                  phx-click="hide_edit_modal"
                  class="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-md shadow-sm hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Save Changes
                </button>
              </div>
            </.form>
          </div>
        </div>
      </div>
    <% end %>

    <%= if @previewing_file do %>
      <div class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col">
          <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
            <div>
              <h3 class="text-lg font-medium text-slate-900">
                Preview File
              </h3>
              <p class="mt-1 text-sm text-slate-500">
                <span class="font-mono">{@previewing_file.path}</span>
                from <span class="font-semibold">{@previewing_file.repository}</span>
              </p>
            </div>
            <button phx-click="hide_file_preview" class="text-slate-400 hover:text-slate-500">
              <.icon name="hero-x-mark" class="w-5 h-5" />
            </button>
          </div>
          <div class="p-6 overflow-auto flex-1">
            <pre class="whitespace-pre-wrap font-mono text-sm bg-slate-50 p-4 rounded-lg border border-slate-200 overflow-x-auto"><code><%= @previewing_file.content %></code></pre>
          </div>
          <div class="px-6 py-4 border-t border-slate-200 flex justify-end">
            <button
              phx-click="hide_file_preview"
              class="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-md shadow-sm hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    <% end %>

    <%= if @show_diff_modal do %>
      <div
        class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50"
        phx-window-keydown="hide_diff_modal"
        phx-key="escape"
      >
        <div class="bg-white rounded-lg shadow-xl max-w-7xl w-full mx-4 max-h-[90vh] flex flex-col">
          <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
            <div class="flex items-center gap-4">
              <h3 class="text-lg font-medium text-slate-900">
                File diff for {@diff_repository.full_name}
              </h3>
              <%= if not Enum.empty?(@available_repositories) do %>
                <div class="relative">
                  <form phx-change="change_diff_repository" class="flex items-center">
                    <select
                      name="repository"
                      class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    >
                      <option value="" disabled selected>Compare with...</option>
                      <%= for repo <- @available_repositories do %>
                        <option value={repo}>{repo}</option>
                      <% end %>
                    </select>
                  </form>
                </div>
              <% end %>
            </div>
            <button phx-click="hide_diff_modal" class="text-slate-400 hover:text-slate-500">
              <.icon name="hero-x-mark" class="w-5 h-5" />
            </button>
          </div>
          <div class="p-6 overflow-auto flex-1" id="diff-container" phx-hook="Diff"></div>
        </div>
      </div>
    <% end %>
    """
  end

  defp all_repositories_have_source_file?(repositories, source_file) do
    non_template_repos = non_template_repositories(repositories)
    repositories_with_source_file(repositories, source_file) == length(non_template_repos)
  end

  defp get_source_files(%{current_user: user, current_organization: organization}) do
    SourceFiles.list_source_files(user, organization)
  end

  defp is_imported?(file, repositories) do
    result =
      Enum.all?(repositories, fn repo ->
        Enum.any?(repo.source_files || [], &(&1.target_path == file["path"]))
      end)

    result
  end

  # Helper functions to filter source files
  defp folder_source_files(source_files, folder) do
    Enum.filter(source_files, fn source_file ->
      Enum.any?(source_file.folders, fn sf_folder -> sf_folder.id == folder.id end)
    end)
  end

  defp global_source_files(source_files) do
    Enum.filter(source_files, fn source_file ->
      Enum.empty?(source_file.folders)
    end)
  end

  defp similarity_color_class(similarity) when similarity >= 90,
    do: "bg-green-500 h-2 rounded-full"

  defp similarity_color_class(similarity) when similarity >= 70,
    do: "bg-yellow-500 h-2 rounded-full"

  defp similarity_color_class(_), do: "bg-red-500 h-2 rounded-full"

  # Helper to extract PR number from GitHub PR URL
  defp extract_pr_number(url) do
    case Regex.run(~r/\/pull\/(\d+)$/, url) do
      [_, number] -> String.to_integer(number)
      _ -> nil
    end
  end

  # Add helper function to find the most similar pair of repositories
  defp find_most_similar_pair(repo_contents), do: Files.find_most_similar_pair(repo_contents)

  # Add helper function to format file size
  defp format_size(size) when is_integer(size) do
    cond do
      size < 1024 -> "#{size} B"
      size < 1024 * 1024 -> "#{Float.round(size / 1024, 1)} KB"
      size < 1024 * 1024 * 1024 -> "#{Float.round(size / 1024 / 1024, 1)} MB"
      true -> "#{Float.round(size / 1024 / 1024 / 1024, 1)} GB"
    end
  end

  defp format_size(_), do: "Unknown size"

  # Helper function to get non-template repositories from a list of repositories
  defp non_template_repositories(repositories) do
    repositories
    |> Enum.reject(& &1.template)
    |> Enum.sort_by(& &1.full_name)
  end

  # Helper functions
  defp format_setting_value(value) when is_list(value), do: value

  defp format_setting_value(value) when is_map(value),
    do: Map.new(value, fn {k, v} -> {to_string(k), format_setting_value(v)} end)

  defp format_setting_value(value) when is_binary(value), do: value
  defp format_setting_value(value) when is_number(value), do: value
  defp format_setting_value(value) when is_boolean(value), do: value
  defp format_setting_value(nil), do: nil
  defp format_setting_value(value), do: to_string(value)

  defp associate_with_repositories(source_file, repositories) do
    results =
      Enum.map(repositories, fn repository ->
        Repobot.Repositories.add_source_file(repository, source_file)
      end)

    case Enum.split_with(results, fn
           {:ok, _} -> true
           _ -> false
         end) do
      {_successes, []} -> {:ok, source_file}
      {_, _failures} -> {:error, :association}
    end
  end

  defp repositories_with_source_file(repositories, source_file) do
    non_template_repos = non_template_repositories(repositories)

    Enum.count(non_template_repos, fn repo ->
      Enum.any?(repo.source_files || [], &(&1.id == source_file.id))
    end)
  end

  # Helper function to determine grid columns based on number of template repositories
  defp template_grid_columns(template_repositories) do
    case length(template_repositories) do
      1 -> "grid-cols-1"
      2 -> "grid-cols-1 md:grid-cols-2"
      _ -> "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
    end
  end

  # Component for expandable source file containers
  attr :id, :string, required: true
  attr :title, :string, required: true
  attr :description, :string, default: nil
  attr :expanded, :boolean, default: true
  attr :class, :string, default: nil
  slot :right_content
  slot :inner_block, required: true

  defp expandable_container(assigns) do
    assigns = assign_new(assigns, :container_id, fn -> "container-#{assigns.id}" end)

    ~H"""
    <style>
      .expandable-container .expandable-header {
        border-radius: 0.5rem;
      }
      .expandable-container .expandable-header.expanded {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom: 1px solid #e2e8f0; /* slate-200 */
      }
      .expandable-container .expandable-header .chevron-icon {
        transform: rotate(0deg);
        transition: transform 0.2s ease-in-out;
      }
      .expandable-container .expandable-header.expanded .chevron-icon {
        transform: rotate(-180deg);
      }
    </style>
    <div
      class={["bg-white rounded-lg shadow-sm border border-slate-200 expandable-container", @class]}
      id={@id}
    >
      <div class={["expandable-header px-6 py-5 bg-slate-50", @expanded && "expanded"]}>
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="flex items-center gap-2">
              <button
                type="button"
                phx-click={
                  JS.toggle(to: "##{@container_id}-content")
                  |> JS.toggle_class("expanded", to: "##{@id} .expandable-header")
                  |> JS.toggle(to: "##{@id} .description")
                }
                class="text-slate-500 hover:text-slate-700 focus:outline-none"
                aria-expanded={@expanded}
              >
                <.icon name="hero-chevron-down" class="w-5 h-5 chevron-icon" />
              </button>
              <h2 class="text-lg font-medium text-slate-900">{@title}</h2>
            </div>
            <%= if @description do %>
              <p class={["mt-1 text-sm text-slate-600 description", !@expanded && "hidden"]}>
                {@description}
              </p>
            <% end %>
          </div>
          <%= if render_slot(@right_content) != [] do %>
            <div>
              {render_slot(@right_content)}
            </div>
          <% end %>
        </div>
      </div>
      <div
        id={"#{@container_id}-content"}
        class={["divide-y divide-slate-200 max-h-[32rem] overflow-y-auto", !@expanded && "hidden"]}
      >
        {render_slot(@inner_block)}
      </div>
    </div>
    """
  end
end
