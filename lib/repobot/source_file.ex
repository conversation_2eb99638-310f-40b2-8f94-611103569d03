defmodule Repobot.SourceFile do
  use Ecto.Schema

  @derive {Jason.Encoder,
           only: [
             :id,
             :user_id,
             :name,
             :target_path,
             :is_template,
             :category_id,
             :source_repository_id,
             :read_only,
             :inserted_at,
             :updated_at
           ]}

  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "source_files" do
    field :name, :string
    field :content, :string
    field :template_vars, :map, default: %{}
    field :target_path, :string
    field :is_template, :boolean, default: false
    field :read_only, :boolean, default: false

    belongs_to :user, Repobot.Accounts.User
    belongs_to :organization, Repobot.Accounts.Organization
    belongs_to :category, Repobot.Category
    belongs_to :source_repository, Repobot.Repository
    has_many :pull_requests, Repobot.PullRequest

    many_to_many :repositories, Repobot.Repository,
      join_through: Repobot.RepositorySourceFile,
      on_replace: :delete

    many_to_many :folders, Repobot.Folder,
      join_through: Repobot.SourceFileFolder,
      on_replace: :delete

    many_to_many :tags, Repobot.Tag,
      join_through: "source_file_tags",
      on_replace: :delete

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(source_file, attrs \\ %{}) do
    source_file
    |> cast(attrs, [
      :name,
      :content,
      :user_id,
      :template_vars,
      :target_path,
      :is_template,
      :organization_id,
      :category_id,
      :source_repository_id,
      :read_only
    ])
    |> validate_required([:name, :user_id, :organization_id])
    |> maybe_handle_template_conversion()
    |> maybe_set_target_path()
    |> validate_required([:target_path])
    |> maybe_extract_template_vars()
  end

  defp maybe_handle_template_conversion(changeset) do
    # Get the current value from the struct data
    current_is_template = changeset.data.is_template
    new_is_template = get_change(changeset, :is_template)
    name = get_field(changeset, :name)
    target_path_change = get_change(changeset, :target_path)

    # Check if we're converting from non-template to template
    case {current_is_template, new_is_template, name, target_path_change} do
      {false, true, name, nil} when not is_nil(name) ->
        # Converting to template and no target_path provided - add .liquid extension and set target_path
        liquid_name = add_liquid_extension(name)

        changeset
        |> put_change(:name, liquid_name)
        |> put_change(:target_path, name)

      _ ->
        changeset
    end
  end

  defp maybe_set_target_path(changeset) do
    # Check if we're in any template conversion scenario
    current_is_template = changeset.data.is_template
    new_is_template = get_change(changeset, :is_template)
    is_converting_to_template = current_is_template == false && new_is_template == true
    is_converting_from_template = current_is_template == true && new_is_template == false
    is_template_conversion = is_converting_to_template || is_converting_from_template

    # Check if target_path is already set (either as a change or in the data)
    target_path_change = get_change(changeset, :target_path)
    current_target_path = changeset.data.target_path
    has_target_path = target_path_change != nil || current_target_path != nil

    name_change = get_change(changeset, :name)

    case {has_target_path, is_template_conversion} do
      {false, false} ->
        # target_path not set and not in template conversion, use name if available
        case name_change do
          nil ->
            changeset

          name ->
            put_change(changeset, :target_path, name)
        end

      _ ->
        # target_path already exists or we're in template conversion, don't override
        changeset
    end
  end

  defp add_liquid_extension(filename) do
    if String.ends_with?(filename, ".liquid") do
      filename
    else
      filename <> ".liquid"
    end
  end

  defp maybe_extract_template_vars(changeset) do
    if get_change(changeset, :content) do
      content = get_field(changeset, :content)
      vars = extract_template_vars(content)
      put_change(changeset, :template_vars, Map.new(vars, &{&1, nil}))
    else
      changeset
    end
  end

  defp extract_template_vars(content) do
    case Solid.parse(content) do
      {:ok, template} ->
        template
        |> scan_names()
        |> MapSet.new()
        |> MapSet.to_list()

      {:error, _reason} ->
        # If parsing fails, assume it's not a template and return empty list
        []
    end
  end

  defp scan_names(template) when is_list(template) do
    template
    |> Enum.flat_map(fn
      {:variable, name} -> [name]
      {_, _, children} when is_list(children) -> scan_names(children)
      _ -> []
    end)
  end

  # Handle non-list templates gracefully
  defp scan_names(_), do: []
end
